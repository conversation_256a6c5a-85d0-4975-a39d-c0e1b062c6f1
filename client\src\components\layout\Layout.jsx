import Navigation from './Navigation';

const Layout = ({ children, showNavigation = true }) => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {showNavigation && <Navigation />}
      
      <div className={`${showNavigation ? 'md:pl-64' : ''} ${showNavigation ? 'pb-16 md:pb-0' : ''}`}>
        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
