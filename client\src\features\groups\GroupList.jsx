import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, Filter } from 'lucide-react';
import GroupCard from './GroupCard';
import AddGroupModal from './AddGroupModal';
import Button from '../../components/ui/Button';
import Skeleton from '../../components/ui/Skeleton';
import storage from '../../utils/storage';

const GroupList = () => {
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [filterType, setFilterType] = useState('all'); // all, active, settled

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = () => {
    setTimeout(() => {
      const guestData = storage.get('guestData', { groups: [] });
      
      // Sample groups for demonstration
      const sampleGroups = [
        {
          id: '1',
          name: 'Weekend Trip',
          icon: '🏖️',
          color: '#3B82F6',
          members: [
            { id: 'user1', name: 'You', email: '<EMAIL>' },
            { id: 'user2', name: 'Alice', email: '<EMAIL>' },
            { id: 'user3', name: 'Bob', email: '<EMAIL>' }
          ],
          balance: 45.50,
          totalExpenses: 320.75,
          createdAt: new Date('2024-01-15'),
          isActive: true
        },
        {
          id: '2',
          name: 'Roommates',
          icon: '🏠',
          color: '#10B981',
          members: [
            { id: 'user1', name: 'You', email: '<EMAIL>' },
            { id: 'user4', name: 'Sarah', email: '<EMAIL>' },
            { id: 'user5', name: 'Mike', email: '<EMAIL>' }
          ],
          balance: -23.25,
          totalExpenses: 156.80,
          createdAt: new Date('2024-01-10'),
          isActive: true
        },
        {
          id: '3',
          name: 'Dinner Club',
          icon: '🍽️',
          color: '#F59E0B',
          members: [
            { id: 'user1', name: 'You', email: '<EMAIL>' },
            { id: 'user6', name: 'Emma', email: '<EMAIL>' },
            { id: 'user7', name: 'David', email: '<EMAIL>' },
            { id: 'user8', name: 'Lisa', email: '<EMAIL>' }
          ],
          balance: 0,
          totalExpenses: 89.40,
          createdAt: new Date('2024-01-05'),
          isActive: false
        }
      ];

      setGroups(guestData.groups.length > 0 ? guestData.groups : sampleGroups);
      setLoading(false);
    }, 800);
  };

  const handleAddGroup = (newGroup) => {
    const updatedGroups = [...groups, { ...newGroup, id: Date.now().toString() }];
    setGroups(updatedGroups);
    
    // Update localStorage
    const guestData = storage.get('guestData', {});
    storage.set('guestData', { ...guestData, groups: updatedGroups });
    
    setShowAddModal(false);
  };

  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         group.members.some(member => 
                           member.name.toLowerCase().includes(searchTerm.toLowerCase())
                         );
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'active' && group.isActive) ||
                         (filterType === 'settled' && !group.isActive);
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton height="h-8" width="w-32" />
          <Skeleton height="h-10" width="w-24" />
        </div>
        <div className="flex space-x-4">
          <Skeleton height="h-10" width="w-64" />
          <Skeleton height="h-10" width="w-32" />
        </div>
        <Skeleton.List items={3} />
      </div>
    );
  }

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <motion.h1
          className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Your Groups
        </motion.h1>
        <Button onClick={() => setShowAddModal(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Group
        </Button>
      </div>

      {/* Search and Filter */}
      <motion.div
        className="flex flex-col sm:flex-row gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search groups or members..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pl-10"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="input-field w-auto"
          >
            <option value="all">All Groups</option>
            <option value="active">Active</option>
            <option value="settled">Settled</option>
          </select>
        </div>
      </motion.div>

      {/* Groups List */}
      {filteredGroups.length > 0 ? (
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, staggerChildren: 0.1 }}
        >
          {filteredGroups.map((group) => (
            <motion.div
              key={group.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <GroupCard group={group} />
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <motion.div
          className="text-center py-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
            <Plus className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {searchTerm ? 'No groups found' : 'No groups yet'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {searchTerm 
              ? 'Try adjusting your search or filter criteria'
              : 'Create your first group to start tracking shared expenses'
            }
          </p>
          {!searchTerm && (
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Group
            </Button>
          )}
        </motion.div>
      )}

      {/* Add Group Modal */}
      <AddGroupModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddGroup}
      />
    </motion.div>
  );
};

export default GroupList;
